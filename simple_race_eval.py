#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简化版RACE评估脚本 - 逐个处理，每个结果立即保存
"""

import json
import time
import os
from datetime import datetime
from utils.api import AIClient
from utils.io_utils import load_jsonl
from prompt.score_prompt_zh import generate_merged_score_prompt as zh_merged_score_prompt
from utils.json_extractor import extract_json_from_markdown
from utils.score_calculator import calculate_weighted_scores

def evaluate_single_report(
    task_data, 
    target_article_data, 
    reference_article_data, 
    criteria_data,
    client,
    max_retries=5
):
    """评估单个报告"""
    
    task_id = task_data.get('id')
    prompt = task_data.get('prompt')
    
    # 准备数据
    target_article = target_article_data.get('article', '')
    reference_article = reference_article_data.get('article', '')
    
    # 格式化评估标准
    criteria_for_prompt = {}
    criterions_dict = criteria_data.get("criterions", criteria_data.get("criteria", {}))
    
    for dim, criterions_list in criterions_dict.items():
        if not isinstance(criterions_list, list):
            continue
        criteria_for_prompt[dim] = []
        for crit_item in criterions_list:
            if isinstance(crit_item, dict) and "criterion" in crit_item and "explanation" in crit_item:
                criteria_for_prompt[dim].append({
                    "criterion": crit_item["criterion"],
                    "explanation": crit_item["explanation"]
                })
    
    criteria_list_str = json.dumps(criteria_for_prompt, ensure_ascii=False, indent=2)
    
    # 构建评估prompt（使用原始中文prompt，不做任何修改）
    user_prompt = zh_merged_score_prompt.format(
        task_prompt=prompt,
        article_1=target_article,
        article_2=reference_article,
        criteria_list=criteria_list_str
    )
    
    print(f"\n任务 {task_id}: Prompt长度 {len(user_prompt)} 字符")
    
    # 重试机制
    for retry in range(max_retries):
        try:
            print(f"  尝试 {retry + 1}/{max_retries}...", end="")
            start_time = time.time()
            
            # 调用API
            response = client.generate(
                user_prompt=user_prompt,
                system_prompt=""
            )
            
            elapsed = time.time() - start_time
            print(f" API响应 ({elapsed:.1f}秒)")
            
            # 提取并解析JSON
            json_str = extract_json_from_markdown(response)
            if not json_str:
                raise ValueError("无法从响应中提取JSON")
            
            result_json = json.loads(json_str)
            
            # 检查必需维度
            required_dims = ["comprehensiveness", "insight", "instruction_following", "readability"]
            missing = [d for d in required_dims if d not in result_json]
            
            if missing:
                print(f"  警告: 缺少维度 {missing}，使用默认值")
                # 为缺失维度添加默认评分
                for dim in missing:
                    if dim not in result_json:
                        result_json[dim] = {
                            "article_1": {"score": 3, "reason": "评估未完成"},
                            "article_2": {"score": 3, "reason": "评估未完成"}
                        }
            
            # 计算加权得分
            scores = calculate_weighted_scores(result_json, criteria_data, "zh")
            
            # 计算归一化得分
            target_total = scores["target"]["total"]
            reference_total = scores["reference"]["total"]
            overall_score = 0
            if target_total + reference_total > 0:
                overall_score = target_total / (target_total + reference_total)
            
            # 计算各维度归一化得分
            normalized_dims = {}
            for dim in required_dims:
                dim_key = f"{dim}_weighted_avg"
                if dim_key in scores["target"]["dims"]:
                    target_score = scores["target"]["dims"][dim_key]
                    reference_score = scores["reference"]["dims"][dim_key]
                    if target_score + reference_score > 0:
                        normalized_dims[dim] = target_score / (target_score + reference_score)
                    else:
                        normalized_dims[dim] = 0
                else:
                    normalized_dims[dim] = 0
            
            # 返回结果
            return {
                "id": task_id,
                "prompt": prompt,
                "comprehensiveness": normalized_dims.get("comprehensiveness", 0),
                "insight": normalized_dims.get("insight", 0),
                "instruction_following": normalized_dims.get("instruction_following", 0),
                "readability": normalized_dims.get("readability", 0),
                "overall_score": overall_score,
                "retry_count": retry + 1,
                "elapsed_time": elapsed
            }
            
        except Exception as e:
            print(f" 失败: {str(e)[:50]}")
            if retry < max_retries - 1:
                time.sleep(2 ** retry)  # 指数退避
            else:
                return {
                    "id": task_id,
                    "prompt": prompt,
                    "error": str(e),
                    "retry_count": max_retries
                }

def main():
    print("=== 简化版RACE评估 ===\n")
    
    # 初始化API客户端
    print("初始化API客户端...")
    client = AIClient()
    print(f"使用模型: {client.model}")
    
    # 加载所有数据
    print("\n加载数据文件...")
    try:
        # 加载查询任务
        all_tasks = load_jsonl("data/subsets/query_subset.jsonl")
        print(f"  查询任务: {len(all_tasks)} 个")
        
        # 加载评估标准
        all_criteria = load_jsonl("data/criteria_data/criteria_subset.jsonl")
        criteria_map = {c['prompt']: c for c in all_criteria}
        print(f"  评估标准: {len(all_criteria)} 个")
        
        # 加载用户文章
        all_target = load_jsonl("data/test_data/cleaned_data/your_model.jsonl")
        target_map = {t['prompt']: t for t in all_target}
        print(f"  用户文章: {len(all_target)} 个")
        
        # 加载参考文章
        all_reference = load_jsonl("data/test_data/cleaned_data/reference.jsonl")
        reference_map = {r['prompt']: r for r in all_reference}
        print(f"  参考文章: {len(all_reference)} 个")
        
    except Exception as e:
        print(f"数据加载失败: {e}")
        return
    
    # 准备输出目录
    output_dir = "results"
    os.makedirs(output_dir, exist_ok=True)
    
    # 检查已完成的结果
    output_file = os.path.join(output_dir, "simple_race_results.jsonl")
    completed_ids = set()
    
    if os.path.exists(output_file):
        try:
            existing = load_jsonl(output_file)
            completed_ids = {r['id'] for r in existing if 'error' not in r}
            print(f"\n已完成: {len(completed_ids)} 个任务")
        except:
            pass
    
    # 过滤待处理任务
    tasks_to_process = []
    for task in all_tasks:
        task_id = task.get('id')
        prompt = task.get('prompt')
        
        # 跳过已完成的
        if task_id in completed_ids:
            continue
            
        # 检查数据完整性
        if prompt not in criteria_map:
            print(f"跳过任务 {task_id}: 缺少评估标准")
            continue
        if prompt not in target_map:
            print(f"跳过任务 {task_id}: 缺少用户文章")
            continue
        if prompt not in reference_map:
            print(f"跳过任务 {task_id}: 缺少参考文章")
            continue
            
        tasks_to_process.append(task)
    
    print(f"\n待处理: {len(tasks_to_process)} 个任务")
    
    if not tasks_to_process:
        print("没有待处理的任务")
        
        # 如果有已完成的结果，输出统计
        if completed_ids:
            print("\n计算已完成任务的统计...")
            all_results = load_jsonl(output_file)
            successful = [r for r in all_results if 'error' not in r]
            
            if successful:
                avg_comp = sum(r['comprehensiveness'] for r in successful) / len(successful)
                avg_insi = sum(r['insight'] for r in successful) / len(successful)
                avg_inst = sum(r['instruction_following'] for r in successful) / len(successful)
                avg_read = sum(r['readability'] for r in successful) / len(successful)
                avg_overall = sum(r['overall_score'] for r in successful) / len(successful)
                
                print(f"\n=== RACE评分结果 ===")
                print(f"成功评估: {len(successful)}/{len(all_results)}")
                print(f"Comprehensiveness:     {avg_comp:.4f}")
                print(f"Insight:               {avg_insi:.4f}")
                print(f"Instruction Following: {avg_inst:.4f}")
                print(f"Readability:           {avg_read:.4f}")
                print(f"Overall Score:         {avg_overall:.4f}")
                print("=" * 30)
        return
    
    # 开始评估
    print(f"\n开始评估 (按Ctrl+C可随时中断)...")
    print("=" * 50)
    
    successful_count = 0
    failed_count = 0
    
    try:
        for i, task in enumerate(tasks_to_process, 1):
            print(f"\n[{i}/{len(tasks_to_process)}] 处理任务 ID={task['id']}")
            
            prompt = task['prompt']
            result = evaluate_single_report(
                task,
                target_map[prompt],
                reference_map[prompt],
                criteria_map[prompt],
                client,
                max_retries=5
            )
            
            # 立即保存结果（追加模式）
            with open(output_file, 'a', encoding='utf-8') as f:
                f.write(json.dumps(result, ensure_ascii=False) + '\n')
            
            if 'error' in result:
                failed_count += 1
                print(f"  ✗ 评估失败: {result['error'][:100]}")
            else:
                successful_count += 1
                print(f"  ✓ 评估成功: Overall={result['overall_score']:.3f}")
            
            # 每5个任务输出一次进度
            if i % 5 == 0:
                print(f"\n进度: {i}/{len(tasks_to_process)} | 成功: {successful_count} | 失败: {failed_count}")
            
    except KeyboardInterrupt:
        print("\n\n用户中断评估")
    
    # 最终统计
    print("\n" + "=" * 50)
    print(f"本次运行: 成功 {successful_count} | 失败 {failed_count}")
    
    # 读取所有结果计算平均分
    try:
        all_results = load_jsonl(output_file)
        successful = [r for r in all_results if 'error' not in r]
        
        if successful:
            avg_comp = sum(r['comprehensiveness'] for r in successful) / len(successful)
            avg_insi = sum(r['insight'] for r in successful) / len(successful)
            avg_inst = sum(r['instruction_following'] for r in successful) / len(successful)
            avg_read = sum(r['readability'] for r in successful) / len(successful)
            avg_overall = sum(r['overall_score'] for r in successful) / len(successful)
            
            print(f"\n=== 总体RACE评分结果 ===")
            print(f"总计成功评估: {len(successful)} 个报告")
            print(f"Comprehensiveness:     {avg_comp:.4f}")
            print(f"Insight:               {avg_insi:.4f}")
            print(f"Instruction Following: {avg_inst:.4f}")
            print(f"Readability:           {avg_read:.4f}")
            print(f"Overall Score:         {avg_overall:.4f}")
            print("=" * 30)
            
            # 保存汇总结果
            summary_file = os.path.join(output_dir, "race_summary.txt")
            with open(summary_file, 'w', encoding='utf-8') as f:
                f.write(f"RACE评分汇总\n")
                f.write(f"生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
                f.write(f"成功评估: {len(successful)} 个报告\n")
                f.write(f"Comprehensiveness:     {avg_comp:.4f}\n")
                f.write(f"Insight:               {avg_insi:.4f}\n")
                f.write(f"Instruction Following: {avg_inst:.4f}\n")
                f.write(f"Readability:           {avg_read:.4f}\n")
                f.write(f"Overall Score:         {avg_overall:.4f}\n")
            
            print(f"\n结果已保存到:")
            print(f"  - {output_file}")
            print(f"  - {summary_file}")
            
    except Exception as e:
        print(f"统计失败: {e}")

if __name__ == "__main__":
    main()