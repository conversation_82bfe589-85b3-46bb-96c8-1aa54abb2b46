#!/usr/bin/env python3
"""
将研究报告文件夹中的.md文件转换为项目需要的JSONL格式
"""
import os
import json
import re
from pathlib import Path

def read_md_file(file_path):
    """读取markdown文件内容"""
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        return content
    except Exception as e:
        print(f"读取文件 {file_path} 失败: {e}")
        return None

def extract_id_from_filename(filename):
    """从文件名中提取ID"""
    # 文件名格式如: 2.md, 52.md 等
    match = re.match(r'(\d+)\.md$', filename)
    if match:
        return int(match.group(1))
    return None

def load_original_queries(query_file):
    """加载原始查询文件"""
    queries = {}
    try:
        with open(query_file, 'r', encoding='utf-8') as f:
            for line in f:
                if line.strip():
                    data = json.loads(line)
                    queries[data['id']] = data
        return queries
    except Exception as e:
        print(f"加载查询文件失败: {e}")
        return {}

def load_original_criteria(criteria_file):
    """加载原始评估标准文件"""
    criteria = {}
    try:
        with open(criteria_file, 'r', encoding='utf-8') as f:
            for line in f:
                if line.strip():
                    data = json.loads(line)
                    criteria[data['id']] = data
        return criteria
    except Exception as e:
        print(f"加载评估标准文件失败: {e}")
        return {}

def convert_reports_to_jsonl(reports_dir, output_file, model_name="your_model"):
    """将研究报告转换为JSONL格式"""
    reports_dir = Path(reports_dir)
    converted_data = []
    
    # 获取所有.md文件
    md_files = list(reports_dir.glob("*.md"))
    print(f"找到 {len(md_files)} 个.md文件")
    
    for md_file in md_files:
        # 提取ID
        file_id = extract_id_from_filename(md_file.name)
        if file_id is None:
            print(f"跳过文件 {md_file.name}，无法提取ID")
            continue
        
        # 读取内容
        content = read_md_file(md_file)
        if content is None:
            continue
        
        # 创建数据条目
        entry = {
            "id": file_id,
            "prompt": "",  # 稍后从原始查询中填充
            "article": content
        }
        
        converted_data.append(entry)
        print(f"转换文件: {md_file.name} -> ID: {file_id}")
    
    # 按ID排序
    converted_data.sort(key=lambda x: x['id'])
    
    # 写入JSONL文件
    try:
        with open(output_file, 'w', encoding='utf-8') as f:
            for entry in converted_data:
                f.write(json.dumps(entry, ensure_ascii=False) + '\n')
        print(f"成功写入 {len(converted_data)} 条记录到 {output_file}")
        return converted_data
    except Exception as e:
        print(f"写入文件失败: {e}")
        return []

def create_subset_files(converted_data, original_queries, original_criteria, output_dir):
    """创建子集文件"""
    output_dir = Path(output_dir)
    output_dir.mkdir(exist_ok=True)
    
    # 获取转换数据中的所有ID
    converted_ids = {item['id'] for item in converted_data}
    print(f"转换的报告ID: {sorted(converted_ids)}")
    
    # 创建查询子集
    query_subset = []
    criteria_subset = []
    
    for item in converted_data:
        item_id = item['id']
        
        # 添加对应的查询
        if item_id in original_queries:
            query_data = original_queries[item_id].copy()
            item['prompt'] = query_data['prompt']  # 填充prompt
            query_subset.append(query_data)
        else:
            print(f"警告: 未找到ID {item_id} 对应的查询")
        
        # 添加对应的评估标准
        if item_id in original_criteria:
            criteria_subset.append(original_criteria[item_id])
        else:
            print(f"警告: 未找到ID {item_id} 对应的评估标准")
    
    # 写入查询子集
    query_subset_file = output_dir / "query_subset.jsonl"
    with open(query_subset_file, 'w', encoding='utf-8') as f:
        for query in query_subset:
            f.write(json.dumps(query, ensure_ascii=False) + '\n')
    print(f"创建查询子集文件: {query_subset_file} ({len(query_subset)} 条)")
    
    # 写入评估标准子集
    criteria_subset_file = output_dir / "criteria_subset.jsonl"
    with open(criteria_subset_file, 'w', encoding='utf-8') as f:
        for criteria in criteria_subset:
            f.write(json.dumps(criteria, ensure_ascii=False) + '\n')
    print(f"创建评估标准子集文件: {criteria_subset_file} ({len(criteria_subset)} 条)")
    
    return query_subset, criteria_subset

def main():
    # 配置路径
    reports_dir = r"D:\Desktop\ellchan\研究报告"
    output_dir = "data/test_data/raw_data"
    model_name = "your_model"
    
    # 原始数据文件
    original_query_file = "data/prompt_data/query.jsonl"
    original_criteria_file = "data/criteria_data/criteria.jsonl"
    
    print("开始转换研究报告...")
    
    # 确保输出目录存在
    os.makedirs(output_dir, exist_ok=True)
    
    # 加载原始数据
    print("加载原始查询和评估标准...")
    original_queries = load_original_queries(original_query_file)
    original_criteria = load_original_criteria(original_criteria_file)
    
    print(f"加载了 {len(original_queries)} 个查询和 {len(original_criteria)} 个评估标准")
    
    # 转换报告
    output_file = os.path.join(output_dir, f"{model_name}.jsonl")
    converted_data = convert_reports_to_jsonl(reports_dir, output_file, model_name)
    
    if not converted_data:
        print("转换失败，退出")
        return
    
    # 创建子集文件
    print("创建子集文件...")
    subset_dir = "data/subsets"
    query_subset, criteria_subset = create_subset_files(
        converted_data, original_queries, original_criteria, subset_dir
    )
    
    # 更新转换数据中的prompt字段并重新写入
    print("更新模型文件中的prompt字段...")
    with open(output_file, 'w', encoding='utf-8') as f:
        for entry in converted_data:
            f.write(json.dumps(entry, ensure_ascii=False) + '\n')
    
    print("\n转换完成！")
    print(f"模型文件: {output_file}")
    print(f"查询子集: data/subsets/query_subset.jsonl")
    print(f"评估标准子集: data/subsets/criteria_subset.jsonl")
    print(f"\n运行评估命令:")
    print(f"python deepresearch_bench_race.py {model_name} --limit 25 --query_file data/subsets/query_subset.jsonl")

if __name__ == "__main__":
    main()