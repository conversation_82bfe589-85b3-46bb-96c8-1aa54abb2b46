#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import json
import time
from utils.api import AIClient
from utils.io_utils import load_jsonl
from prompt.score_prompt_zh import generate_merged_score_prompt as zh_merged_score_prompt
from utils.json_extractor import extract_json_from_markdown

def test_batch_style():
    """用批量评估的确切方式测试单个报告"""
    
    print("=== 模拟批量评估方式测试 ===\n")
    
    # 1. 完全按照批量评估的方式加载数据
    print("1. 按批量评估方式加载数据...")
    
    # 加载所有数据（模拟批量评估的加载方式）
    all_tasks = load_jsonl("data/subsets/query_subset.jsonl")
    all_criteria = load_jsonl("data/criteria_data/criteria_subset.jsonl")
    all_target = load_jsonl("data/test_data/cleaned_data/your_model.jsonl")
    all_reference = load_jsonl("data/test_data/cleaned_data/reference.jsonl")
    
    print(f"   加载任务: {len(all_tasks)}")
    print(f"   加载标准: {len(all_criteria)}")
    print(f"   加载目标: {len(all_target)}")
    print(f"   加载参考: {len(all_reference)}")
    
    # 2. 找到ID=2的任务（和testone.jsonl中的一样）
    target_task = None
    for task in all_tasks:
        if task.get('id') == 2:
            target_task = task
            break
    
    if not target_task:
        print("✗ 找不到ID=2的任务")
        return
        
    print(f"✓ 找到目标任务: ID={target_task.get('id')}")
    
    # 3. 完全按照批量评估的逻辑处理
    task_id = target_task.get('id')
    prompt = target_task.get('prompt')
    
    # 找到匹配的数据
    criteria_match = next((c for c in all_criteria if c.get('prompt') == prompt), None)
    target_match = next((t for t in all_target if t.get('prompt') == prompt), None)
    reference_match = next((r for r in all_reference if r.get('prompt') == prompt), None)
    
    if not all([criteria_match, target_match, reference_match]):
        print("✗ 数据匹配失败")
        return
    
    print("✓ 数据匹配成功")
    
    # 4. 完全按照批量评估的方式构建prompt
    print("\n2. 构建评估prompt...")
    
    try:
        criteria_list_str = json.dumps(criteria_match.get('criteria', {}), ensure_ascii=False)
    except Exception as e:
        print(f"✗ 标准格式化失败: {e}")
        return
    
    target_article = target_match.get('article', '')
    reference_article = reference_match.get('article', '')
    
    # 使用和批量评估完全相同的prompt构建方式
    merged_score_prompt = zh_merged_score_prompt  # 和批量评估一样强制使用中文
    
    user_prompt = merged_score_prompt.format(
        task_prompt=prompt,
        article_1=target_article,
        article_2=reference_article,
        criteria_list=criteria_list_str 
    )
    
    print(f"   Prompt长度: {len(user_prompt)} 字符")
    
    # 5. 完全按照批量评估的方式调用API（包括重试逻辑）
    print("\n3. 按批量评估方式调用API...")
    
    llm_client = AIClient()
    max_retries = 10
    retry_count = 0
    success = False
    
    print(f"   使用模型: {llm_client.model}")
    print(f"   最大重试: {max_retries}")
    
    while retry_count < max_retries and not success:
        print(f"\n   尝试 {retry_count + 1}/{max_retries}...")
        start_time = time.time()
        
        try:
            llm_response_str = llm_client.generate(
                user_prompt=user_prompt,
                system_prompt=""
            )
            
            elapsed = time.time() - start_time
            print(f"   ✓ API调用成功 (耗时: {elapsed:.1f}秒)")
            print(f"   响应长度: {len(llm_response_str) if llm_response_str else 0} 字符")

            # Extract JSON from response
            json_str_extracted = extract_json_from_markdown(llm_response_str)
            if not json_str_extracted:
                raise ValueError("Failed to extract JSON from LLM response")
                
            llm_output_json = json.loads(json_str_extracted)
            
            # Check if all required dimensions exist
            expected_dims = ["comprehensiveness", "insight", "instruction_following", "readability"]
            if not all(dim in llm_output_json for dim in expected_dims):
                missing_dims = [dim for dim in expected_dims if dim not in llm_output_json]
                raise ValueError(f"Missing expected dimensions: {missing_dims}")
            
            # All checks passed
            success = True
            print("   ✓ JSON解析和验证成功!")
            print(f"   包含维度: {list(llm_output_json.keys())}")
            
        except Exception as e:
            elapsed = time.time() - start_time
            retry_count += 1
            print(f"   ✗ 失败 (耗时: {elapsed:.1f}秒): {str(e)[:100]}...")
            
            if retry_count < max_retries:
                sleep_time = 1.5 ** retry_count
                print(f"   等待 {sleep_time:.1f} 秒后重试...")
                time.sleep(sleep_time)
            else:
                print("   达到最大重试次数，放弃")
                break
    
    if success:
        print(f"\n✓ 批量评估方式测试成功!")
    else:
        print(f"\n✗ 批量评估方式测试失败")

if __name__ == "__main__":
    test_batch_style()