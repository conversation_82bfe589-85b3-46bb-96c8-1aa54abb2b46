# 深度研究报告质量评估系统技术文档

## 1. 系统概述

### 1.1 RACE评估框架
RACE (Research Article Comprehensive Evaluation) 是一个基于AI的深度研究报告质量评估系统，通过多维度动态评估机制对研究报告进行客观、全面的质量评价。

### 1.2 核心特性
- **动态标准生成**：根据具体任务自动生成针对性评估标准
- **多维度评估**：从四个关键维度全面评价报告质量
- **比较评分机制**：通过对比评估确保评分的相对客观性
- **加权计算**：基于任务特性动态调整各维度权重

## 2. 评估维度体系

### 2.1 四大核心维度

#### 2.1.1 全面性 (Comprehensiveness)
- **定义**：信息覆盖的广度、深度和相关性
- **评估要点**：
  - 问题覆盖度：是否全面回答任务中的所有问题
  - 技术广度：是否覆盖相关领域的关键技术和材料
  - 背景深度：是否置于完整的技术背景下讨论

#### 2.1.2 洞察力 (Insight)
- **定义**：分析的深度、独到性、逻辑性和结论价值
- **评估要点**：
  - 数据驱动分析：是否提供量化的、有数据支撑的分析
  - 趋势与挑战识别：是否识别行业关键趋势和技术瓶颈
  - 信息整合与关联：是否有效关联不同领域信息
  - 研究空白识别：是否明确指出研究空白和未解决问题

#### 2.1.3 指令遵循能力 (Instruction Following)
- **定义**：报告是否准确、完整地回应任务要求和限定条件
- **评估要点**：
  - 核心任务响应：是否紧密围绕任务核心要求
  - 范围界定：是否严格遵守任务范围限定

#### 2.1.4 可读性 (Readability)
- **定义**：结构清晰度、语言流畅度、数据呈现效果和整体易理解性
- **评估要点**：
  - 结构与逻辑：文章结构是否清晰、逻辑性强
  - 信息呈现效率：是否有效利用表格、列表等形式
  - 专业性与可信度：是否展现专业性并建立可信度

## 3. 标准生成流程

### 3.1 两阶段生成机制

#### 3.1.1 第一阶段：维度权重生成
```python
# 核心流程：utils/generate_criteria.py
def generate_weights_multiple_times(item_id, prompt, language, sample_count):
    # 多次采样生成权重
    weights_samples = []
    for i in range(sample_count):
        # 调用LLM生成权重
        weight_output = ai_client.generate(user_prompt=weight_prompt)
        parsed_weights = parse_llm_output_as_json(weight_output)
        weights_samples.append(parsed_weights)
    
    # 计算平均权重并归一化
    avg_weights = calculate_average_weights(weights_samples)
    return round_weights_and_adjust(avg_weights, decimal_places=2)
```

**权重生成特点**：
- 根据具体任务动态调整权重
- 多次采样取平均值确保稳定性
- 权重总和严格等于1.0
- 考虑任务特性和核心价值点

#### 3.1.2 第二阶段：具体标准生成
```python
# 为每个维度生成详细评判标准
for dim_name, criteria_prompt_template in criteria_prompts.items():
    user_prompt_criteria = criteria_prompt_template.format(task_prompt=prompt)
    criteria_output = ai_client.generate(user_prompt=user_prompt_criteria)
    parsed_criteria = parse_llm_output_as_json(criteria_output, expected_type=list)
    current_criterions[dim_name] = parsed_criteria
```

**标准生成特点**：
- 每个维度有专门的prompt模板
- 生成具体的评判细则和权重
- 标准必须与任务紧密相关
- 包含详细的解释和理由

### 3.2 标准数据结构
```json
{
    "id": "task_id",
    "prompt": "原始任务描述",
    "dimension_weight": {
        "comprehensiveness": 0.25,
        "insight": 0.35,
        "instruction_following": 0.25,
        "readability": 0.15
    },
    "criterions": {
        "comprehensiveness": [
            {
                "criterion": "问题覆盖度：是否全面回答任务中的所有问题",
                "explanation": "评估标准的详细解释",
                "weight": 0.4
            }
        ]
    }
}
```

## 4. 评分计算机制

### 4.1 评分执行流程

#### 4.1.1 LLM评分调用
```python
# 核心评分prompt：prompt/score_prompt_zh.py
def evaluate_single_report(task, target_article, reference_article, criteria_data, client):
    # 构建评分prompt
    user_prompt = zh_merged_score_prompt.format(
        task_prompt=task['prompt'],
        article_1=target_article['response'],
        article_2=reference_article['response'],
        criteria_list=format_criteria_list(criteria_data)
    )
    
    # 调用LLM进行评分
    response = client.generate(user_prompt)
    
    # 解析JSON结果
    json_str = extract_json_from_markdown(response)
    result_json = json.loads(json_str)
    
    return result_json
```

#### 4.1.2 评分数据结构
```json
{
    "comprehensiveness": [
        {
            "criterion": "问题覆盖度",
            "analysis": "详细的对比分析",
            "article_1_score": 10,
            "article_2_score": 8.5
        }
    ],
    "insight": [...],
    "instruction_following": [...],
    "readability": [...]
}
```

### 4.2 分数计算逻辑

#### 4.2.1 加权平均计算
```python
# utils/score_calculator.py
def calculate_weighted_scores(llm_output_json, criteria_data, language="en"):
    results = {
        "target": {"dims": {}, "total": 0.0},
        "reference": {"dims": {}, "total": 0.0}
    }
    
    # 获取维度权重和标准权重
    dimension_weights = criteria_data.get("dimension_weight", {})
    criterion_weights = {}
    for dim, criterions in criteria_data.get("criterions", {}).items():
        criterion_weights[dim] = {crit['criterion']: crit['weight'] for crit in criterions}
    
    # 计算每个维度的加权平均分
    for dim, scores_list in llm_output_json.items():
        dim_target_weighted_sum = 0.0
        dim_reference_weighted_sum = 0.0
        dim_total_weight = 0.0
        
        for score_item in scores_list:
            criterion_text = score_item.get("criterion")
            article_1_score = float(score_item.get("article_1_score", 0))
            article_2_score = float(score_item.get("article_2_score", 0))
            
            # 查找对应的权重
            weight = criterion_weights[dim].get(criterion_text, default_weight)
            
            dim_target_weighted_sum += article_1_score * weight
            dim_reference_weighted_sum += article_2_score * weight
            dim_total_weight += weight
        
        # 计算维度平均分
        dim_target_avg = dim_target_weighted_sum / dim_total_weight
        dim_reference_avg = dim_reference_weighted_sum / dim_total_weight
        
        results["target"]["dims"][f"{dim}_weighted_avg"] = dim_target_avg
        results["reference"]["dims"][f"{dim}_weighted_avg"] = dim_reference_avg
    
    return results
```

#### 4.2.2 归一化处理
```python
# 计算归一化得分
target_total = scores["target"]["total"]
reference_total = scores["reference"]["total"]
overall_score = 0
if target_total + reference_total > 0:
    overall_score = target_total / (target_total + reference_total)

# 计算各维度归一化得分
normalized_dims = {}
for dim in required_dims:
    dim_key = f"{dim}_weighted_avg"
    if dim_key in scores["target"]["dims"]:
        target_score = scores["target"]["dims"][dim_key]
        reference_score = scores["reference"]["dims"][dim_key]
        if target_score + reference_score > 0:
            normalized_dims[dim] = target_score / (target_score + reference_score)
        else:
            normalized_dims[dim] = 0
```

### 4.3 最终结果格式
```json
{
    "id": "task_id",
    "prompt": "任务描述",
    "comprehensiveness": 0.5405,
    "insight": 0.7692,
    "instruction_following": 0.5128,
    "readability": 0.6154,
    "overall_score": 0.6154,
    "retry_count": 1,
    "elapsed_time": 45.2
}
```

## 5. 关键代码模块分析

### 5.1 JSON解析模块 (utils/json_extractor.py)
- **功能**：从LLM响应中提取和验证JSON数据
- **特点**：多种解析策略，容错性强
- **关键方法**：`extract_json_from_markdown()`

### 5.2 评估标准生成模块 (utils/generate_criteria.py)
- **功能**：动态生成任务特定的评估标准
- **特点**：两阶段生成，多次采样
- **关键方法**：`process_single_item_sequential()`

### 5.3 分数计算模块 (utils/score_calculator.py)
- **功能**：加权计算和归一化处理
- **特点**：支持多种评分模式，权重匹配
- **关键方法**：`calculate_weighted_scores()`

### 5.4 评估执行模块 (simple_race_eval.py)
- **功能**：协调整个评估流程
- **特点**：错误处理，进度跟踪
- **关键方法**：`evaluate_single_report()`

## 6. 实际评估案例分析

基于debug_response_33.txt的评估案例：

### 6.1 任务背景
- **任务ID**：33
- **评估对象**：两篇关于芯片工艺金属沉积设备的深度研究报告
- **评估维度**：comprehensiveness, insight, instruction_adherence, readability

### 6.2 评估结果摘要
- **全面性维度**：文章1在问题覆盖度(10 vs 8.5)、技术广度(10 vs 8.5)、背景深度(10 vs 9.0)方面均优于文章2
- **洞察力维度**：文章1在数据驱动分析(10 vs 6.0)、研究空白识别(10 vs 5.0)方面显著优于文章2
- **指令遵循**：两篇文章都很好地遵循了任务要求
- **可读性**：文章1在信息呈现效率和专业性方面更胜一筹

### 6.3 评估特点
- **详细分析**：每个评判标准都有详细的对比分析
- **量化评分**：0-10分的精确评分
- **客观理由**：基于具体内容特征的评分理由

## 7. 系统优势与特点

### 7.1 动态适应性
- 根据任务特性自动调整评估标准
- 权重分配反映任务核心价值点

### 7.2 评估客观性
- 比较评分减少绝对评分的主观性
- 多维度评估避免单一指标偏差

### 7.3 结果可解释性
- 详细的评分理由和分析过程
- 清晰的数据结构和计算逻辑

### 7.4 系统稳定性
- 多次采样确保权重稳定性
- 容错机制处理异常情况

## 8. 详细技术实现

### 8.1 评估标准生成的Prompt工程

#### 8.1.1 维度权重生成Prompt
```python
# prompt/criteria_prompt_zh.py
generate_eval_dimension_weight_prompt = """
<system_role>
你是一名经验丰富的调研文章评估专家。你擅长根据具体的调研任务，深刻理解其目标、挑战与核心价值点，并据此为后续的文章质量评估设定动态、合理、且有充分理由支撑的维度权重。
</system_role>

<user_prompt>
现在有一个深度调研任务，内容如下：
<task>
"{task_prompt}"
</task>

**评估公式**：总评分 = 全面性 * 全面性权重 + 洞察力 * 洞察力权重 + 指令遵循能力 * 指令遵循能力权重 + 可读性 * 可读性权重。（注意：所有权重之和必须精确等于 1.0）
"""
```

#### 8.1.2 具体标准生成Prompt
每个维度都有专门的prompt模板，以洞察力维度为例：
```python
generate_eval_criteria_prompt_insight = """
<system_role>
你是一名经验丰富的调研文章评估专家。你擅长将抽象的评估维度（如"洞察力"）分解为针对具体调研任务的、可操作的、清晰的评判细则。
</system_role>

**核心要求**：
1. 紧扣任务：分析、标准、解释和权重都必须直接关联任务的核心要求和特性
2. 超越表面：标准应评估分析的深度、逻辑严谨性、见解的独到性和结论的价值
3. 理由充分：需清晰阐述制定这些标准和权重的总体思路
4. 权重合理：权重分配需有逻辑，反映各项在洞察力维度内的相对重要程度
"""
```

### 8.2 评分执行的核心逻辑

#### 8.2.1 评分Prompt构建
```python
# prompt/score_prompt_zh.py
generate_merged_score_prompt = """
<system_role>你是一名严格、细致、客观的调研文章评估专家。你擅长根据具体的评估标准，深入比较两篇针对同一任务的文章，并给出精确的评分和清晰的理由。</system_role>

<user_prompt>
**任务背景**
有一个深度调研任务，你需要评估针对该任务撰写的两篇调研文章。我们会从以下四个维度评估文章：全面性、洞察力、指令遵循能力和可读性。

<task>
"{task_prompt}"
</task>

**待评估文章**
<article_1>
"{article_1}"
</article_1>

<article_2>
"{article_2}"
</article_2>

**评估标准**
现在，你需要根据以下评判标准列表，逐条评估并比较这两篇文章的表现，输出对比分析，然后给出0-10的分数。

<criteria_list>
{criteria_list}
</criteria_list>
"""
```

#### 8.2.2 JSON解析的多策略机制
```python
# utils/json_extractor.py
def extract_json_from_markdown(text):
    """多策略JSON提取机制"""

    # 策略1：直接解析完整文本
    if text.strip().startswith('{') and text.strip().endswith('}'):
        try:
            json.loads(text.strip())
            return text.strip()
        except json.JSONDecodeError:
            pass

    # 策略2：提取```json代码块
    if "```json" in text and "```" in text[text.find("```json")+7:]:
        start = text.find("```json") + 7
        end = text.find("```", start)
        if end > start:
            json_str = text[start:end].strip()
            try:
                json.loads(json_str)
                return json_str
            except json.JSONDecodeError:
                pass

    # 策略3：正则表达式匹配
    match = re.search(r"```json\s*([\s\S]*?)\s*```", text)
    if match:
        json_str = match.group(1).strip()
        try:
            json.loads(json_str)
            return json_str
        except json.JSONDecodeError:
            pass

    # 策略4：大括号嵌套匹配
    start = text.find('{')
    if start != -1:
        level = 0
        for i, char in enumerate(text[start:]):
            if char == '{':
                level += 1
            elif char == '}':
                level -= 1
                if level == 0:
                    end = start + i + 1
                    potential_json = text[start:end]
                    try:
                        json.loads(potential_json)
                        return potential_json
                    except json.JSONDecodeError:
                        pass
                    break

    return None
```

### 8.3 权重匹配和分数计算

#### 8.3.1 标准匹配机制
```python
# utils/score_calculator.py
def calculate_weighted_scores(llm_output_json, criteria_data, language="en"):
    # 创建标准文本到权重的映射
    criterion_weights = {}
    for dim, criterions in criteria_data.get("criterions", {}).items():
        criterion_weights[dim] = {crit['criterion']: crit['weight'] for crit in criterions}

    # 记录未匹配的标准
    unmatched_criteria = set()

    for dim, scores_list in llm_output_json.items():
        dim_target_weighted_sum = 0.0
        dim_reference_weighted_sum = 0.0
        dim_total_weight = 0.0

        dim_criteria_map = criterion_weights.get(dim, {})

        for score_item in scores_list:
            criterion_text = score_item.get("criterion", "").strip()
            article_1_score = float(score_item.get("article_1_score", 0))
            article_2_score = float(score_item.get("article_2_score", 0))

            # 精确匹配
            weight = dim_criteria_map.get(criterion_text)

            # 模糊匹配（如果精确匹配失败）
            if weight is None:
                for criteria_key in dim_criteria_map.keys():
                    if criterion_text in criteria_key or criteria_key in criterion_text:
                        weight = dim_criteria_map[criteria_key]
                        break

            # 使用平均权重作为后备
            if weight is None:
                unmatched_criteria.add(f"{dim}:{criterion_text}")
                weight = sum(dim_criteria_map.values()) / len(dim_criteria_map)

            dim_target_weighted_sum += article_1_score * weight
            dim_reference_weighted_sum += article_2_score * weight
            dim_total_weight += weight

        # 计算维度平均分
        if dim_total_weight > 0:
            dim_target_avg = dim_target_weighted_sum / dim_total_weight
            dim_reference_avg = dim_reference_weighted_sum / dim_total_weight
        else:
            dim_target_avg = 0
            dim_reference_avg = 0

        results["target"]["dims"][f"{dim}_weighted_avg"] = dim_target_avg
        results["reference"]["dims"][f"{dim}_weighted_avg"] = dim_reference_avg

    return results
```

#### 8.3.2 归一化计算公式
```python
# 总分归一化：target_score / (target_score + reference_score)
overall_score = target_total / (target_total + reference_total) if (target_total + reference_total) > 0 else 0

# 各维度归一化
for dim in required_dims:
    target_score = scores["target"]["dims"][f"{dim}_weighted_avg"]
    reference_score = scores["reference"]["dims"][f"{dim}_weighted_avg"]
    normalized_dims[dim] = target_score / (target_score + reference_score) if (target_score + reference_score) > 0 else 0
```

## 9. 数据流转和存储

### 9.1 输入数据格式

#### 9.1.1 任务数据 (query.jsonl)
```json
{
    "id": "33",
    "prompt": "调研如今先进制程的芯片工艺中，哪几种设备用来沉积金属？沉积什么金属？为什么选择这些设备？",
    "language": "zh"
}
```

#### 9.1.2 文章数据 (your_model.jsonl, reference.jsonl)
```json
{
    "id": "33",
    "prompt": "调研如今先进制程的芯片工艺中...",
    "response": "# 先进制程芯片工艺中的金属沉积设备与材料选择\n\n## 1. 背景与概述..."
}
```

### 9.2 中间数据格式

#### 9.2.1 评估标准数据 (criteria.jsonl)
```json
{
    "id": "33",
    "prompt": "调研如今先进制程的芯片工艺中...",
    "dimension_weight": {
        "comprehensiveness": 0.25,
        "insight": 0.35,
        "instruction_following": 0.25,
        "readability": 0.15
    },
    "criterions": {
        "comprehensiveness": [
            {
                "criterion": "问题覆盖度 (Question Coverage): 是否全面地回答了任务中提出的所有问题",
                "explanation": "评估文章是否完整回答了'哪几种设备'、'沉积什么金属'、'为什么选择'这三个核心问题",
                "weight": 0.4
            },
            {
                "criterion": "技术广度 (Technology Breadth): 是否全面地覆盖了任务中提到的所有设备类型",
                "explanation": "评估是否覆盖PVD、CVD、E-beam、ALD、MBE等设备类型和相关金属材料",
                "weight": 0.35
            },
            {
                "criterion": "背景深度 (Contextual Depth): 是否将讨论置于先进制程的完整背景下",
                "explanation": "评估是否在3nm/2nm节点、FEOL/MOL/BEOL等先进制程背景下讨论",
                "weight": 0.25
            }
        ],
        "insight": [...],
        "instruction_following": [...],
        "readability": [...]
    }
}
```

#### 9.2.2 LLM评分输出 (debug_response_33.txt)
```json
{
    "comprehensiveness": [
        {
            "criterion": "问题覆盖度 (Question Coverage): 是否全面地回答了任务中提出的所有问题",
            "analysis": "两篇文章都回答了任务中的三个核心问题。文章1通过一个核心的'设备-金属-工艺选择矩阵'表格，极为高效和精确地回答了'哪种设备-什么金属'的问题...",
            "article_1_score": 10,
            "article_2_score": 8.5
        }
    ],
    "insight": [...],
    "instruction_adherence": [...],
    "readability": [...]
}
```

### 9.3 最终输出格式

#### 9.3.1 评估结果 (race_results.jsonl)
```json
{
    "id": "33",
    "prompt": "调研如今先进制程的芯片工艺中...",
    "comprehensiveness": 0.5405,
    "insight": 0.7692,
    "instruction_following": 0.5128,
    "readability": 0.6154,
    "overall_score": 0.6154,
    "retry_count": 1,
    "elapsed_time": 45.2
}
```

#### 9.3.2 汇总报告 (race_summary.txt)
```
RACE评分汇总
生成时间: 2024-01-15 14:30:25
成功评估: 50 个报告
Comprehensiveness:     0.5234
Insight:               0.6789
Instruction Following: 0.5456
Readability:           0.5678
Overall Score:         0.5789
```

## 10. 错误处理和容错机制

### 10.1 重试机制
```python
def evaluate_single_report(task, target_article, reference_article, criteria_data, client, max_retries=5):
    for retry in range(max_retries):
        try:
            # 执行评估
            response = client.generate(user_prompt)
            json_str = extract_json_from_markdown(response)
            result_json = json.loads(json_str)

            # 检查必需维度
            required_dims = ["comprehensiveness", "insight", "instruction_following", "readability"]
            missing = [d for d in required_dims if d not in result_json]

            if missing:
                # 为缺失维度添加默认评分
                for dim in missing:
                    result_json[dim] = {
                        "article_1": {"score": 3, "reason": "评估未完成"},
                        "article_2": {"score": 3, "reason": "评估未完成"}
                    }

            return process_successful_result(result_json, criteria_data)

        except Exception as e:
            if retry < max_retries - 1:
                time.sleep(2 ** retry)  # 指数退避
            else:
                return {
                    "id": task['id'],
                    "prompt": task['prompt'],
                    "error": str(e),
                    "retry_count": max_retries
                }
```

### 10.2 数据验证
```python
def validate_evaluation_result(result_json, required_dims):
    """验证评估结果的完整性和有效性"""

    # 检查必需维度
    missing_dims = [dim for dim in required_dims if dim not in result_json]
    if missing_dims:
        raise ValueError(f"Missing required dimensions: {missing_dims}")

    # 检查评分格式
    for dim, scores_list in result_json.items():
        if not isinstance(scores_list, list):
            raise ValueError(f"Dimension '{dim}' should be a list")

        for score_item in scores_list:
            if not isinstance(score_item, dict):
                raise ValueError(f"Score item in '{dim}' should be a dictionary")

            required_fields = ["criterion", "article_1_score", "article_2_score"]
            missing_fields = [field for field in required_fields if field not in score_item]
            if missing_fields:
                raise ValueError(f"Missing fields in score item: {missing_fields}")

    return True
```

### 10.3 异常情况处理
- **JSON解析失败**：使用多策略解析，最后回退到模式匹配
- **维度缺失**：使用默认评分填充
- **权重匹配失败**：使用平均权重作为后备
- **网络超时**：指数退避重试机制
- **评分格式错误**：数据验证和格式转换

## 11. 性能优化和扩展性

### 11.1 并发处理
```python
# 支持多线程并发评估
def process_multiple_tasks_concurrent(tasks, max_workers=25):
    with concurrent.futures.ThreadPoolExecutor(max_workers=max_workers) as executor:
        futures = []
        for task in tasks:
            future = executor.submit(evaluate_single_report, task, ...)
            futures.append(future)

        for future in concurrent.futures.as_completed(futures):
            result = future.result()
            # 处理结果
```

### 11.2 缓存机制
- **标准缓存**：相同任务的评估标准可以复用
- **结果缓存**：避免重复评估相同的文章对
- **权重缓存**：相似任务的权重可以参考

### 11.3 可扩展性设计
- **多语言支持**：通过prompt模板切换支持中英文
- **评估维度扩展**：可以轻松添加新的评估维度
- **评分模式扩展**：支持单文章评分和对比评分
- **后端模型切换**：支持不同的LLM后端

## 12. 总结

RACE深度研究报告质量评估系统通过以下核心机制实现了客观、全面、动态的报告质量评估：

1. **动态标准生成**：根据具体任务自动生成针对性的评估标准和权重
2. **多维度评估**：从全面性、洞察力、指令遵循、可读性四个维度全面评价
3. **比较评分机制**：通过对比评估减少主观性，提高评分客观性
4. **加权计算**：基于任务特性和标准重要性进行加权平均
5. **归一化处理**：将绝对分数转换为相对分数，便于比较和理解

该系统在保证评估质量的同时，具备良好的扩展性和容错性，能够适应不同类型的研究报告评估需求。
