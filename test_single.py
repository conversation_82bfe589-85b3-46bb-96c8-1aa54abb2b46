#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import json
import time
from utils.api import AIClient
from utils.io_utils import load_jsonl
from prompt.score_prompt_zh import generate_merged_score_prompt as zh_merged_score_prompt
from utils.json_extractor import extract_json_from_markdown

def test_single_report():
    """测试单个研究报告的评估"""
    
    print("=== 单个报告评估测试 ===\n")
    
    # 1. 加载测试数据
    print("1. 加载测试数据...")
    try:
        test_data = load_jsonl("testone.jsonl")
        if not test_data:
            print("✗ testone.jsonl为空或不存在")
            return
        
        report = test_data[0]
        print(f"✓ 加载成功")
        print(f"   报告ID: {report.get('id', 'N/A')}")
        print(f"   任务长度: {len(report.get('prompt', ''))} 字符")
        print(f"   文章长度: {len(report.get('article', ''))} 字符")
        
    except Exception as e:
        print(f"✗ 加载失败: {e}")
        return
    
    # 2. 加载参考数据
    print("\n2. 加载参考数据...")
    try:
        # 加载对应的查询和参考文章
        queries = load_jsonl("data/subsets/query_subset.jsonl")
        references = load_jsonl("data/test_data/cleaned_data/reference.jsonl")
        criteria = load_jsonl("data/criteria_data/criteria_subset.jsonl")
        
        # 找到匹配的数据
        target_prompt = report.get('prompt')
        query_match = next((q for q in queries if q.get('prompt') == target_prompt), None)
        ref_match = next((r for r in references if r.get('prompt') == target_prompt), None)
        criteria_match = next((c for c in criteria if c.get('prompt') == target_prompt), None)
        
        if not all([query_match, ref_match, criteria_match]):
            print("✗ 找不到匹配的参考数据")
            print(f"   查询匹配: {'✓' if query_match else '✗'}")
            print(f"   参考匹配: {'✓' if ref_match else '✗'}")
            print(f"   标准匹配: {'✓' if criteria_match else '✗'}")
            return
            
        print("✓ 参考数据加载成功")
        
    except Exception as e:
        print(f"✗ 参考数据加载失败: {e}")
        return
    
    # 3. 准备评估数据
    print("\n3. 准备评估数据...")
    task_prompt = report.get('prompt', '')
    target_article = report.get('article', '')
    reference_article = ref_match.get('article', '')
    criteria_list = json.dumps(criteria_match.get('criteria', {}), ensure_ascii=False)
    
    print(f"   任务prompt: {len(task_prompt)} 字符")
    print(f"   目标文章: {len(target_article)} 字符")
    print(f"   参考文章: {len(reference_article)} 字符")
    print(f"   评估标准: {len(criteria_list)} 字符")
    
    # 4. 构建评估prompt
    print("\n4. 构建评估prompt...")
    eval_prompt = zh_merged_score_prompt.format(
        task_prompt=task_prompt,
        article_1=target_article,
        article_2=reference_article,
        criteria_list=criteria_list
    )
    
    total_prompt_length = len(eval_prompt)
    print(f"   总prompt长度: {total_prompt_length} 字符")
    
    if total_prompt_length > 100000:  # 约100k字符
        print("   ⚠️  警告: prompt非常长，可能导致超时")
    elif total_prompt_length > 50000:  # 约50k字符
        print("   ⚠️  注意: prompt较长，可能影响响应速度")
    else:
        print("   ✓ prompt长度合理")
    
    # 5. 测试API调用
    print("\n5. 开始API调用...")
    client = AIClient()
    print(f"   使用模型: {client.model}")
    
    start_time = time.time()
    try:
        response = client.generate(eval_prompt)
        elapsed = time.time() - start_time
        
        print(f"✓ API调用成功!")
        print(f"   耗时: {elapsed:.1f} 秒")
        print(f"   响应长度: {len(response) if response else 0} 字符")
        print(f"   响应类型: {type(response)}")
        print(f"   响应是否为None: {response is None}")
        print(f"   响应是否为空字符串: {response == ''}")
        
        if response:
            print(f"   响应前100字符: {repr(response[:100])}")
        else:
            print("   ⚠️  响应为空或None!")
        
        # 6. 尝试解析JSON
        print("\n6. 解析响应...")
        try:
            json_content = extract_json_from_markdown(response)
            parsed = json.loads(json_content)
            
            print("✓ JSON解析成功!")
            print(f"   包含维度: {list(parsed.keys())}")
            
            # 检查必需维度
            required_dims = ["comprehensiveness", "insight", "instruction_following", "readability"]
            missing = [d for d in required_dims if d not in parsed]
            
            if missing:
                print(f"   ✗ 缺少维度: {missing}")
            else:
                print("   ✓ 包含所有必需维度")
                
        except Exception as e:
            print(f"   ✗ JSON解析失败: {e}")
            print(f"   响应前500字符: {response[:500]}...")
            
    except Exception as e:
        elapsed = time.time() - start_time
        print(f"✗ API调用失败 (耗时: {elapsed:.1f}秒)")
        print(f"   错误: {e}")
        
        # 分析可能的原因
        if "504" in str(e) or "timeout" in str(e).lower():
            print("\n   可能原因:")
            print("   - prompt太长导致处理时间超过代理服务器限制")
            print("   - 模型处理复杂任务需要更长时间")
            print("   - 建议使用更快的模型或简化任务")

if __name__ == "__main__":
    test_single_report()