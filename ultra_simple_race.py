#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
超简版RACE评估 - 最核心逻辑
"""

import json
import time
import os
from utils.api import AIClient
from utils.io_utils import load_jsonl
from prompt.score_prompt_zh import generate_merged_score_prompt as zh_prompt
from utils.json_extractor import extract_json_from_markdown

def main():
    print("=== 超简版RACE评估 ===\n")
    
    # 初始化
    client = AIClient()
    print(f"使用模型: {client.model}")
    
    # 加载数据
    tasks = load_jsonl("data/subsets/query_subset.jsonl")
    criteria = load_jsonl("data/criteria_data/criteria_subset.jsonl") 
    targets = load_jsonl("data/test_data/cleaned_data/your_model.jsonl")
    references = load_jsonl("data/test_data/cleaned_data/reference.jsonl")
    
    # 建立映射
    criteria_map = {c['prompt']: c for c in criteria}
    target_map = {t['prompt']: t for t in targets}
    ref_map = {r['prompt']: r for r in references}
    
    print(f"加载了 {len(tasks)} 个任务\n")
    
    results = []
    
    for i, task in enumerate(tasks, 1):
        task_id = task['id']
        prompt = task['prompt']
        
        print(f"[{i}/{len(tasks)}] 任务 {task_id}")
        
        # 检查数据
        if prompt not in criteria_map or prompt not in target_map or prompt not in ref_map:
            print("  跳过：数据不完整")
            continue
        
        # 准备数据
        criteria_json = json.dumps(criteria_map[prompt]['criterions'], ensure_ascii=False)
        target_article = target_map[prompt]['article']
        ref_article = ref_map[prompt]['article']
        
        # 构建prompt
        eval_prompt = zh_prompt.format(
            task_prompt=prompt,
            article_1=target_article,
            article_2=ref_article,
            criteria_list=criteria_json
        )
        
        print(f"  Prompt长度: {len(eval_prompt)} 字符")
        
        # 调用API
        try:
            start = time.time()
            response = client.generate(eval_prompt)
            elapsed = time.time() - start
            
            print(f"  API响应: {elapsed:.1f}秒")
            
            # 解析JSON
            json_str = extract_json_from_markdown(response)
            scores = json.loads(json_str)
            
            # 计算归一化分数
            result = {"id": task_id}
            
            for dim in ["comprehensiveness", "insight", "instruction_following", "readability"]:
                if dim in scores:
                    target_score = scores[dim]["article_1"]["score"]
                    ref_score = scores[dim]["article_2"]["score"] 
                    total = target_score + ref_score
                    result[dim] = target_score / total if total > 0 else 0
                else:
                    result[dim] = 0
                    print(f"    警告: 缺少维度 {dim}")
            
            # 计算总分
            result["overall"] = sum(result[dim] for dim in ["comprehensiveness", "insight", "instruction_following", "readability"]) / 4
            
            results.append(result)
            print(f"  ✓ 完成: Overall={result['overall']:.3f}")
            
        except Exception as e:
            print(f"  ✗ 失败: {str(e)[:50]}")
            results.append({"id": task_id, "error": str(e)})
    
    # 保存结果
    os.makedirs("results", exist_ok=True)
    with open("results/ultra_simple_results.jsonl", "w", encoding="utf-8") as f:
        for r in results:
            f.write(json.dumps(r, ensure_ascii=False) + "\n")
    
    # 计算平均分
    successful = [r for r in results if "error" not in r]
    if successful:
        avg_comp = sum(r["comprehensiveness"] for r in successful) / len(successful)
        avg_insight = sum(r["insight"] for r in successful) / len(successful) 
        avg_inst = sum(r["instruction_following"] for r in successful) / len(successful)
        avg_read = sum(r["readability"] for r in successful) / len(successful)
        avg_overall = sum(r["overall"] for r in successful) / len(successful)
        
        print(f"\n=== 最终结果 ===")
        print(f"成功: {len(successful)}/{len(results)}")
        print(f"Comprehensiveness:     {avg_comp:.4f}")
        print(f"Insight:               {avg_insight:.4f}")
        print(f"Instruction Following: {avg_inst:.4f}")
        print(f"Readability:           {avg_read:.4f}")
        print(f"Overall Score:         {avg_overall:.4f}")
        
        # 保存汇总
        with open("results/ultra_simple_summary.txt", "w", encoding="utf-8") as f:
            f.write(f"成功: {len(successful)}/{len(results)}\n")
            f.write(f"Comprehensiveness: {avg_comp:.4f}\n")
            f.write(f"Insight: {avg_insight:.4f}\n")
            f.write(f"Instruction Following: {avg_inst:.4f}\n")
            f.write(f"Readability: {avg_read:.4f}\n")
            f.write(f"Overall Score: {avg_overall:.4f}\n")

if __name__ == "__main__":
    main()