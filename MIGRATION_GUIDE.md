# API 迁移指南：从 Google Gemini SDK 到 OpenAI 兼容格式

## 概述

项目已从 Google 官方的 `google-genai` SDK 迁移到 OpenAI 兼容格式，支持使用国内转发的 Gemini API 服务。

## 主要变更

### 1. 依赖库变更
- ❌ 移除：`google-genai`
- ✅ 添加：`openai>=1.0.0`

### 2. 环境变量变更
| 旧变量 | 新变量 | 说明 |
|--------|--------|------|
| `GEMINI_API_KEY` | `OPENAI_API_KEY` | API 密钥 |
| - | `OPENAI_BASE_URL` | API 基础 URL（必需） |
| - | `OPENAI_MODEL` | 默认模型名称 |
| - | `FACT_MODEL` | 事实检查模型 |

### 3. API 调用格式变更
- 从 Google Gemini 原生格式改为 OpenAI Chat Completions 格式
- 支持 system 和 user 消息角色
- 移除了 Google 特有的 `thinking_config` 配置

## 配置步骤

### 1. 安装新依赖
```bash
pip install -r requirements.txt
```

### 2. 配置环境变量
复制示例配置文件：
```bash
cp .env.example .env
```

编辑 `.env` 文件，填入你的配置：
```env
# 你的 API 密钥
OPENAI_API_KEY=your_api_key_here

# 国内转发的 API 地址（必须以 /v1 结尾）
OPENAI_BASE_URL=https://your-proxy-domain.com/v1

# 使用的模型名称
OPENAI_MODEL=gemini-2.0-flash-thinking-exp
FACT_MODEL=gemini-2.0-flash-thinking-exp

# Jina API（保持不变）
JINA_API_KEY=your_jina_api_key_here
```

### 3. 测试配置
运行测试脚本验证配置：
```bash
python test_api.py
```

## 支持的模型

通过 OpenAI 兼容格式支持的 Gemini 模型：
- `gemini-2.0-flash-thinking-exp`
- `gemini-1.5-pro`
- `gemini-1.5-flash`
- `gemini-1.5-pro-002`
- `gemini-1.5-flash-002`

## 使用方法

### 基本用法
```python
from utils.api import AIClient

# 使用默认配置
client = AIClient()

# 生成回复
response = client.generate(
    user_prompt="你好，请介绍一下自己",
    system_prompt="你是一个有用的助手"
)
print(response)
```

### 自定义配置
```python
# 使用自定义配置
client = AIClient(
    api_key="your_key",
    base_url="https://your-domain.com/v1",
    model="gemini-1.5-pro"
)
```

## 兼容性说明

- 保持了原有的 `call_model()` 函数接口不变
- 主程序 `deepresearch_bench_race.py` 无需修改
- Jina 网页抓取功能保持不变

## 故障排除

### 常见错误

1. **API key not provided**
   - 检查 `OPENAI_API_KEY` 环境变量是否设置

2. **Base URL not provided**
   - 检查 `OPENAI_BASE_URL` 环境变量是否设置
   - 确保 URL 以 `/v1` 结尾

3. **Connection failed**
   - 检查网络连接
   - 验证 API 地址是否正确
   - 确认 API 密钥是否有效

### 测试连接
```python
from utils.api import AIClient

client = AIClient()
if client.test_connection():
    print("连接成功！")
else:
    print("连接失败，请检查配置")
```

## 注意事项

1. **URL 格式**：`OPENAI_BASE_URL` 必须以 `/v1` 结尾
2. **模型名称**：使用你的转发服务支持的确切模型名称
3. **超时设置**：默认超时时间为 600 秒，适合长文本生成
4. **向后兼容**：保留了旧的变量名以确保兼容性

迁移完成后，你就可以使用国内转发的 Gemini API 服务了！