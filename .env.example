# OpenAI 兼容 API 配置
# 你的 API 密钥
OPENAI_API_KEY=your_api_key_here

# 国内转发的 API 基础 URL（必须以 /v1 结尾）
OPENAI_BASE_URL=https://your-proxy-domain.com/v1

# 使用的模型名称（Gemini 模型通过 OpenAI 兼容格式）
OPENAI_MODEL=gemini-2.0-flash-thinking-exp

# 用于事实检查的模型
FACT_MODEL=gemini-2.0-flash-thinking-exp

# Jina API 配置（用于网页抓取，保持不变）
JINA_API_KEY=your_jina_api_key_here

# 常用的 Gemini 模型选项：
# - gemini-2.0-flash-thinking-exp
# - gemini-1.5-pro
# - gemini-1.5-flash
# - gemini-1.5-pro-002
# - gemini-1.5-flash-002