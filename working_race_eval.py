#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
基于test_single.py成功经验的RACE评估脚本
"""

import json
import time
import os
from utils.api import AIClient
from utils.io_utils import load_jsonl
from prompt.score_prompt_zh import generate_merged_score_prompt as zh_merged_score_prompt
from utils.json_extractor import extract_json_from_markdown
from utils.score_calculator import calculate_weighted_scores

def main():
    print("=== 基于成功经验的RACE评估 ===\n")
    
    # 初始化API客户端
    client = AIClient()
    print(f"使用模型: {client.model}")
    
    # 完全按照test_single.py的方式加载数据
    print("\n加载数据...")
    try:
        # 加载所有数据（和test_batch_single.py一样）
        all_tasks = load_jsonl("data/subsets/query_subset.jsonl")
        all_criteria = load_jsonl("data/criteria_data/criteria_subset.jsonl")
        all_target = load_jsonl("data/test_data/cleaned_data/your_model.jsonl")
        all_reference = load_jsonl("data/test_data/cleaned_data/reference.jsonl")
        
        print(f"  加载任务: {len(all_tasks)}")
        print(f"  加载标准: {len(all_criteria)}")
        print(f"  加载目标: {len(all_target)}")
        print(f"  加载参考: {len(all_reference)}")
        
    except Exception as e:
        print(f"数据加载失败: {e}")
        return
    
    # 准备输出
    os.makedirs("results", exist_ok=True)
    output_file = "results/working_race_results.jsonl"
    
    # 检查已完成的任务
    completed_ids = set()
    if os.path.exists(output_file):
        try:
            existing = load_jsonl(output_file)
            completed_ids = {r['id'] for r in existing if 'error' not in r}
            print(f"\n已完成: {len(completed_ids)} 个任务")
        except:
            pass
    
    results = []
    successful = 0
    failed = 0
    
    print(f"\n开始逐个评估...")
    print("=" * 50)
    
    for i, task in enumerate(all_tasks, 1):
        task_id = task.get('id')
        prompt = task.get('prompt')
        
        # 跳过已完成的
        if task_id in completed_ids:
            print(f"[{i}/{len(all_tasks)}] 任务 {task_id} - 已完成，跳过")
            continue
        
        print(f"\n[{i}/{len(all_tasks)}] 处理任务 {task_id}")
        
        # 完全按照test_single.py的方式找匹配数据
        criteria_match = next((c for c in all_criteria if c.get('prompt') == prompt), None)
        target_match = next((t for t in all_target if t.get('prompt') == prompt), None)
        reference_match = next((r for r in all_reference if r.get('prompt') == prompt), None)
        
        if not all([criteria_match, target_match, reference_match]):
            print("  跳过：数据不完整")
            failed += 1
            results.append({"id": task_id, "error": "数据不完整"})
            continue
        
        # 完全按照test_single.py的方式准备数据
        try:
            criteria_list_str = json.dumps(criteria_match.get('criteria', {}), ensure_ascii=False)
        except Exception as e:
            print(f"  跳过：标准格式化失败 - {e}")
            failed += 1
            results.append({"id": task_id, "error": f"标准格式化失败: {e}"})
            continue
        
        target_article = target_match.get('article', '')
        reference_article = reference_match.get('article', '')
        
        # 完全按照test_single.py的方式构建prompt
        user_prompt = zh_merged_score_prompt.format(
            task_prompt=prompt,
            article_1=target_article,
            article_2=reference_article,
            criteria_list=criteria_list_str 
        )
        
        print(f"  Prompt长度: {len(user_prompt)} 字符")
        
        # 完全按照test_single.py的方式调用API
        try:
            start_time = time.time()
            response = client.generate(user_prompt)
            elapsed = time.time() - start_time
            
            print(f"  API响应: {elapsed:.1f}秒")
            
            # 完全按照test_single.py的方式解析
            json_str_extracted = extract_json_from_markdown(response)
            if not json_str_extracted:
                raise ValueError("Failed to extract JSON from LLM response")
                
            llm_output_json = json.loads(json_str_extracted)
            
            # 检查必需维度
            expected_dims = ["comprehensiveness", "insight", "instruction_following", "readability"]
            missing_dims = [dim for dim in expected_dims if dim not in llm_output_json]
            
            if missing_dims:
                print(f"  警告: 缺少维度 {missing_dims}")
                # 为缺失维度添加默认值
                for dim in missing_dims:
                    llm_output_json[dim] = [{"article_1": {"score": 3}, "article_2": {"score": 3}}]
            
            # 使用原始的加权分数计算方法
            try:
                scores = calculate_weighted_scores(llm_output_json, criteria_match, "zh")
                
                # 计算总分 = target / (target + reference)
                target_total = scores["target"]["total"]
                reference_total = scores["reference"]["total"]
                overall_score = 0
                if target_total + reference_total > 0:
                    overall_score = target_total / (target_total + reference_total)
                
                # 计算各维度归一化分数
                result = {"id": task_id}
                for dim in expected_dims:
                    dim_key = f"{dim}_weighted_avg"
                    if dim_key in scores["target"]["dims"]:
                        target_score = scores["target"]["dims"][dim_key]
                        reference_score = scores["reference"]["dims"][dim_key]
                        if target_score + reference_score > 0:
                            result[dim] = target_score / (target_score + reference_score)
                        else:
                            result[dim] = 0
                    else:
                        result[dim] = 0
                
                result["overall"] = overall_score
                
            except Exception as e:
                print(f"  分数计算失败: {e}")
                # 使用默认分数
                result = {"id": task_id}
                for dim in expected_dims:
                    result[dim] = 0.5
                result["overall"] = 0.5
            
            # 立即保存结果
            with open(output_file, 'a', encoding='utf-8') as f:
                f.write(json.dumps(result, ensure_ascii=False) + '\n')
            
            successful += 1
            print(f"  ✓ 成功: Overall={result['overall']:.3f}")
            print(f"    详细分数: C={result['comprehensiveness']:.3f} I={result['insight']:.3f} IF={result['instruction_following']:.3f} R={result['readability']:.3f}")
            print(f"    响应长度: {len(response)} 字符")
            print(f"    JSON长度: {len(json_str_extracted)} 字符")
            print(f"    解析后维度: {list(llm_output_json.keys())}")
            
            # 保存完整响应到调试文件
            debug_file = f"results/debug_response_{task_id}.txt"
            with open(debug_file, 'w', encoding='utf-8') as f:
                f.write(f"任务ID: {task_id}\n")
                f.write(f"响应长度: {len(response)} 字符\n")
                f.write(f"JSON长度: {len(json_str_extracted)} 字符\n")
                f.write(f"解析后维度: {list(llm_output_json.keys())}\n\n")
                f.write("=== 完整响应 ===\n")
                f.write(response)
                f.write("\n\n=== 提取的JSON ===\n")
                f.write(json_str_extracted)
                f.write("\n\n=== 解析后的数据 ===\n")
                f.write(json.dumps(llm_output_json, ensure_ascii=False, indent=2))
            
            results.append(result)
            
        except Exception as e:
            elapsed = time.time() - start_time if 'start_time' in locals() else 0
            print(f"  ✗ 失败 ({elapsed:.1f}秒): {str(e)[:50]}")
            
            error_result = {"id": task_id, "error": str(e)}
            results.append(error_result)
            
            # 也保存失败的结果
            with open(output_file, 'a', encoding='utf-8') as f:
                f.write(json.dumps(error_result, ensure_ascii=False) + '\n')
            
            failed += 1
    
    # 最终统计
    print("\n" + "=" * 50)
    print(f"评估完成: 成功 {successful} | 失败 {failed}")
    
    if successful > 0:
        # 计算成功任务的平均分
        successful_results = [r for r in results if "error" not in r]
        
        avg_comp = sum(r["comprehensiveness"] for r in successful_results) / len(successful_results)
        avg_insight = sum(r["insight"] for r in successful_results) / len(successful_results)
        avg_inst = sum(r["instruction_following"] for r in successful_results) / len(successful_results)
        avg_read = sum(r["readability"] for r in successful_results) / len(successful_results)
        avg_overall = sum(r["overall"] for r in successful_results) / len(successful_results)
        
        print(f"\n=== RACE评分结果 ===")
        print(f"Comprehensiveness:     {avg_comp:.4f}")
        print(f"Insight:               {avg_insight:.4f}")
        print(f"Instruction Following: {avg_inst:.4f}")
        print(f"Readability:           {avg_read:.4f}")
        print(f"Overall Score:         {avg_overall:.4f}")
        print("=" * 25)
        
        # 保存汇总
        with open("results/working_race_summary.txt", "w", encoding="utf-8") as f:
            f.write(f"成功评估: {successful}/{successful + failed}\n")
            f.write(f"Comprehensiveness: {avg_comp:.4f}\n")
            f.write(f"Insight: {avg_insight:.4f}\n")
            f.write(f"Instruction Following: {avg_inst:.4f}\n")
            f.write(f"Readability: {avg_read:.4f}\n")
            f.write(f"Overall Score: {avg_overall:.4f}\n")

if __name__ == "__main__":
    main()